import { sql } from 'drizzle-orm';
import { db } from './db';
import * as schema from '@shared/schema';
import { migrate } from 'drizzle-orm/postgres-js/migrator';

export async function initializeDatabase() {
  console.log('Initializing database...');

  try {
    // Run migrations to create tables
    await migrate(db, { migrationsFolder: './migrations' });
    console.log('Database migrations completed successfully');
    return true;
  } catch (error) {
    console.error('Error initializing database:', error);
    // If migrations fail, try to create tables manually
    try {
      await createTablesManually();
      console.log('Manual table creation completed successfully');
      return true;
    } catch (manualError) {
      console.error('Error creating tables manually:', manualError);
      return false;
    }
  }
}

async function createTablesManually() {
  // Create tables using raw SQL if migrations fail
  await db.execute(sql`
    CREATE TABLE IF NOT EXISTS users (
      id SERIAL PRIMARY KEY,
      username TEXT NOT NULL UNIQUE,
      password TEXT NOT NULL,
      full_name TEXT NOT NULL,
      email TEXT NOT NULL UNIQUE,
      phone TEXT,
      date_of_birth TEXT,
      gender TEXT,
      blood_type TEXT,
      allergies TEXT,
      chronic_conditions TEXT,
      emergency_contact_name TEXT,
      emergency_contact_phone TEXT,
      avatar_url TEXT,
      created_at TIMESTAMP DEFAULT NOW()
    )
  `);

  await db.execute(sql`
    CREATE TABLE IF NOT EXISTS medical_records (
      id SERIAL PRIMARY KEY,
      user_id INTEGER NOT NULL REFERENCES users(id),
      title TEXT NOT NULL,
      description TEXT,
      record_type TEXT NOT NULL,
      provider TEXT,
      provider_type TEXT,
      record_date TIMESTAMP NOT NULL,
      file_content TEXT NOT NULL,
      file_type TEXT NOT NULL,
      file_name TEXT NOT NULL,
      file_size INTEGER NOT NULL,
      tags TEXT[],
      ai_summary TEXT,
      created_at TIMESTAMP DEFAULT NOW()
    )
  `);

  await db.execute(sql`
    CREATE TABLE IF NOT EXISTS health_metrics (
      id SERIAL PRIMARY KEY,
      user_id INTEGER NOT NULL REFERENCES users(id),
      metric_type TEXT NOT NULL,
      value TEXT NOT NULL,
      unit TEXT NOT NULL,
      recorded_at TIMESTAMP NOT NULL,
      notes TEXT,
      created_at TIMESTAMP DEFAULT NOW()
    )
  `);

  await db.execute(sql`
    CREATE TABLE IF NOT EXISTS appointments (
      id SERIAL PRIMARY KEY,
      user_id INTEGER NOT NULL REFERENCES users(id),
      title TEXT NOT NULL,
      description TEXT,
      appointment_type TEXT NOT NULL,
      provider_name TEXT NOT NULL,
      provider_type TEXT,
      location TEXT,
      appointment_date TIMESTAMP NOT NULL,
      duration INTEGER,
      reminder_set BOOLEAN DEFAULT FALSE,
      reminder_time TIMESTAMP,
      status TEXT DEFAULT 'scheduled',
      notes TEXT,
      created_at TIMESTAMP DEFAULT NOW()
    )
  `);

  await db.execute(sql`
    CREATE TABLE IF NOT EXISTS family_members (
      id SERIAL PRIMARY KEY,
      user_id INTEGER NOT NULL REFERENCES users(id),
      name TEXT NOT NULL,
      relationship TEXT NOT NULL,
      date_of_birth TEXT,
      gender TEXT,
      blood_type TEXT,
      allergies TEXT,
      chronic_conditions TEXT,
      avatar_url TEXT,
      created_at TIMESTAMP DEFAULT NOW()
    )
  `);

  await db.execute(sql`
    CREATE TABLE IF NOT EXISTS ai_chat_history (
      id SERIAL PRIMARY KEY,
      user_id INTEGER NOT NULL REFERENCES users(id),
      message TEXT NOT NULL,
      response TEXT NOT NULL,
      created_at TIMESTAMP DEFAULT NOW()
    )
  `);

  await db.execute(sql`
    CREATE TABLE IF NOT EXISTS smartwatch_devices (
      id SERIAL PRIMARY KEY,
      user_id INTEGER NOT NULL REFERENCES users(id),
      device_name TEXT NOT NULL,
      device_type TEXT NOT NULL,
      device_id TEXT NOT NULL UNIQUE,
      is_connected BOOLEAN DEFAULT FALSE,
      last_sync TIMESTAMP,
      created_at TIMESTAMP DEFAULT NOW()
    )
  `);
}

// Create a default admin user if no users exist
export async function createDefaultUserIfNeeded() {
  try {
    const existingUsers = await db.query.users.findMany({
      limit: 1
    });

    if (existingUsers.length === 0) {
      console.log('No users found, creating default user...');

      // Hash for password "password123"
      const hashedPassword = '$2b$10$3euPcmQFCiblsZeEu5s7p.9MQICjYJ7ogs/D3Q1vIwLRrJfQ7mNZS';

      await db.insert(schema.users).values({
        username: 'manasvi',
        password: hashedPassword,
        fullName: 'Manasvi',
        email: '<EMAIL>',
        phone: '1234567890',
        gender: 'Male',
        bloodType: 'O+',
      });

      console.log('Default user created successfully');
    } else {
      console.log('Users already exist, skipping default user creation');
    }
  } catch (error) {
    console.error('Error creating default user:', error);
  }
}


