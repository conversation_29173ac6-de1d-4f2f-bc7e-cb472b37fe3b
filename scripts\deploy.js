#!/usr/bin/env node

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🚀 Starting deployment process...');

try {
  // 1. Install dependencies
  console.log('📦 Installing dependencies...');
  execSync('npm install', { stdio: 'inherit' });

  // 2. Build the frontend
  console.log('🏗️  Building frontend...');
  execSync('npm run build:frontend', { stdio: 'inherit' });

  // 3. Build the backend
  console.log('🔧 Building backend...');
  execSync('npx esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist', { stdio: 'inherit' });

  // 4. Copy necessary files
  console.log('📁 Copying files...');
  
  // Copy migrations
  if (fs.existsSync('migrations')) {
    execSync('cp -r migrations dist/', { stdio: 'inherit' });
  }

  // Copy package.json for production dependencies
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const prodPackageJson = {
    name: packageJson.name,
    version: packageJson.version,
    type: packageJson.type,
    dependencies: packageJson.dependencies,
    scripts: {
      start: 'node index.js'
    }
  };
  
  fs.writeFileSync('dist/package.json', JSON.stringify(prodPackageJson, null, 2));

  console.log('✅ Deployment build completed successfully!');
  console.log('📝 Next steps:');
  console.log('1. Set up your PostgreSQL database (Neon/Supabase)');
  console.log('2. Configure environment variables in Vercel');
  console.log('3. Deploy to Vercel');

} catch (error) {
  console.error('❌ Deployment build failed:', error.message);
  process.exit(1);
}
