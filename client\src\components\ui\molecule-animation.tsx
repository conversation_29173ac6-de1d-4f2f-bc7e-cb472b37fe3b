import { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';

interface MoleculeNode {
  x: number;
  y: number;
  radius: number;
  vx: number;
  vy: number;
  color: string;
}

interface MoleculeProps {
  color?: string;
  nodeCount?: number;
  opacity?: number;
}

export function MoleculeAnimation({
  color = '#3b82f6',
  nodeCount = 15,
  opacity = 0.3
}: MoleculeProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Create molecule nodes
    const nodes: MoleculeNode[] = [];
    for (let i = 0; i < nodeCount; i++) {
      nodes.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        radius: Math.random() * 6 + 3, // Larger nodes
        vx: (Math.random() - 0.5) * 1.0, // Faster movement
        vy: (Math.random() - 0.5) * 1.0, // Faster movement
        color
      });
    }

    // Draw function
    const draw = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Update and draw nodes
      nodes.forEach(node => {
        // Update position
        node.x += node.vx;
        node.y += node.vy;

        // Bounce off edges
        if (node.x < node.radius || node.x > canvas.width - node.radius) {
          node.vx *= -1;
        }

        if (node.y < node.radius || node.y > canvas.height - node.radius) {
          node.vy *= -1;
        }

        // Draw node with enhanced glow
        ctx.beginPath();
        ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
        ctx.fillStyle = node.color;
        ctx.globalAlpha = 0.8; // More opaque
        ctx.fill();

        // Draw inner glow
        const innerGradient = ctx.createRadialGradient(
          node.x, node.y, 0,
          node.x, node.y, node.radius
        );
        innerGradient.addColorStop(0, `${node.color}FF`); // Full opacity at center
        innerGradient.addColorStop(1, `${node.color}40`); // Fade out at edge

        ctx.beginPath();
        ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
        ctx.fillStyle = innerGradient;
        ctx.globalAlpha = 0.9;
        ctx.fill();

        // Draw outer glow
        const outerGradient = ctx.createRadialGradient(
          node.x, node.y, node.radius * 0.8,
          node.x, node.y, node.radius * 3 // Larger glow
        );
        outerGradient.addColorStop(0, `${node.color}80`);
        outerGradient.addColorStop(1, `${node.color}00`);

        ctx.beginPath();
        ctx.arc(node.x, node.y, node.radius * 3, 0, Math.PI * 2);
        ctx.fillStyle = outerGradient;
        ctx.globalAlpha = 0.5; // More visible glow
        ctx.fill();
        ctx.globalAlpha = 1;
      });

      // Draw enhanced connections
      for (let i = 0; i < nodes.length; i++) {
        for (let j = i + 1; j < nodes.length; j++) {
          const dx = nodes[i].x - nodes[j].x;
          const dy = nodes[i].y - nodes[j].y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          // Increase connection distance
          if (distance < 200) {
            // Create gradient for connection
            const gradient = ctx.createLinearGradient(
              nodes[i].x, nodes[i].y,
              nodes[j].x, nodes[j].y
            );
            gradient.addColorStop(0, `${nodes[i].color}90`);
            gradient.addColorStop(1, `${nodes[j].color}90`);

            ctx.beginPath();
            ctx.moveTo(nodes[i].x, nodes[i].y);
            ctx.lineTo(nodes[j].x, nodes[j].y);
            ctx.strokeStyle = gradient;
            ctx.lineWidth = 2 * (1 - distance / 200); // Thicker lines
            ctx.globalAlpha = 0.5 * (1 - distance / 200); // More visible
            ctx.stroke();

            // Add pulse effect to connections
            if (distance < 100) {
              const pulseSize = (Math.sin(Date.now() * 0.003) + 1) * 0.5;
              ctx.beginPath();
              ctx.moveTo(nodes[i].x, nodes[i].y);
              ctx.lineTo(nodes[j].x, nodes[j].y);
              ctx.strokeStyle = gradient;
              ctx.lineWidth = 1 * pulseSize;
              ctx.globalAlpha = 0.3 * pulseSize;
              ctx.stroke();
            }
          }
        }
      }
      ctx.globalAlpha = 1;
    };

    // Animation loop
    const animate = () => {
      draw();
      requestAnimationFrame(animate);
    };

    const animationId = requestAnimationFrame(animate);

    // Cleanup
    return () => {
      window.removeEventListener('resize', resizeCanvas);
      cancelAnimationFrame(animationId);
    };
  }, [color, nodeCount]);

  return (
    <motion.canvas
      ref={canvasRef}
      className="fixed inset-0 pointer-events-none z-0"
      initial={{ opacity: 0 }}
      animate={{ opacity }}
      transition={{ duration: 1 }}
    />
  );
}
