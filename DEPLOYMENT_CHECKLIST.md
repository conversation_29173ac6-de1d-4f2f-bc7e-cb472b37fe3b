# MediKey Deployment Checklist

## Pre-Deployment Setup

### 1. Database Setup (Choose One)

#### Option A: Neon (Recommended)
- [ ] Go to [neon.tech](https://neon.tech)
- [ ] Create free account
- [ ] Create new project: "medikey-db"
- [ ] Copy connection string
- [ ] Format: `****************************************************************`

#### Option B: Supabase
- [ ] Go to [supabase.com](https://supabase.com)
- [ ] Create free account
- [ ] Create new project
- [ ] Go to Settings > Database
- [ ] Copy connection string

### 2. OpenAI API Setup
- [ ] Go to [platform.openai.com](https://platform.openai.com)
- [ ] Create account (free $5 credits)
- [ ] Go to API Keys section
- [ ] Create new API key
- [ ] Copy key (starts with `sk-`)

### 3. GitHub Repository
- [ ] Push all code to GitHub
- [ ] Ensure main branch is up to date
- [ ] Verify all files are committed

## Vercel Deployment

### 1. Vercel Account Setup
- [ ] Go to [vercel.com](https://vercel.com)
- [ ] Sign up with GitHub account
- [ ] Connect GitHub repository

### 2. Project Import
- [ ] Click "New Project"
- [ ] Select your MediKey repository
- [ ] Click "Import"

### 3. Build Configuration
- [ ] Framework Preset: **Other**
- [ ] Build Command: `npm run vercel-build`
- [ ] Output Directory: `dist`
- [ ] Install Command: `npm install`

### 4. Environment Variables
Add these in Vercel Settings > Environment Variables:

- [ ] `DATABASE_URL` = Your Neon/Supabase connection string
- [ ] `SESSION_SECRET` = Random secure string (generate new one)
- [ ] `OPENAI_API_KEY` = Your OpenAI API key
- [ ] `NODE_ENV` = `production`

### 5. Deploy
- [ ] Click "Deploy"
- [ ] Wait for deployment to complete
- [ ] Check deployment logs for errors

## Post-Deployment Testing

### 1. Basic Functionality
- [ ] Visit deployed URL
- [ ] Check if homepage loads
- [ ] Verify no console errors

### 2. Authentication
- [ ] Try to sign in with default credentials:
  - Username: `manasvi`
  - Password: `password123`
- [ ] Test sign out
- [ ] Test sign up with new account

### 3. Database Features
- [ ] Create a medical record
- [ ] View medical records
- [ ] Add health metrics
- [ ] Schedule an appointment

### 4. AI Features
- [ ] Open AI Assistant
- [ ] Ask a health question
- [ ] Verify AI responds correctly

### 5. Mobile Access
- [ ] Go to Mobile Access page
- [ ] Verify QR code displays
- [ ] Scan QR code with mobile device
- [ ] Verify mobile site loads

## Troubleshooting

### Common Issues and Solutions

#### Database Connection Error
- [ ] Verify DATABASE_URL is correct
- [ ] Check database is active in Neon/Supabase
- [ ] Ensure SSL mode is enabled

#### Build Failures
- [ ] Check build logs in Vercel
- [ ] Verify all dependencies in package.json
- [ ] Test build locally: `npm run build`

#### API Routes Not Working
- [ ] Check vercel.json configuration
- [ ] Verify API routes start with `/api/`
- [ ] Check function logs in Vercel

#### OpenAI API Errors
- [ ] Verify API key is correct
- [ ] Check remaining credits
- [ ] Ensure key has proper permissions

### Debug Commands (Local)
```bash
npm run db:test      # Test database connection
npm run check        # TypeScript check
npm run build        # Test build process
```

## Security Checklist

- [ ] Changed default SESSION_SECRET
- [ ] Using HTTPS (automatic with Vercel)
- [ ] Database uses SSL
- [ ] API keys stored securely
- [ ] No sensitive data in client code

## Performance Checklist

- [ ] Images optimized
- [ ] Bundle size reasonable
- [ ] Database queries optimized
- [ ] Caching configured

## Final Verification

- [ ] All features working
- [ ] No console errors
- [ ] Mobile responsive
- [ ] Fast loading times
- [ ] Secure connections

## Custom Domain (Optional)

- [ ] Go to Vercel project settings
- [ ] Click "Domains"
- [ ] Add custom domain
- [ ] Configure DNS records

## Monitoring Setup

- [ ] Enable Vercel Analytics
- [ ] Set up error tracking
- [ ] Monitor database performance
- [ ] Check API usage limits

## Backup Strategy

- [ ] Database backups enabled (automatic with Neon/Supabase)
- [ ] Code backed up in GitHub
- [ ] Environment variables documented

## Documentation

- [ ] Update README with deployment URL
- [ ] Document any custom configurations
- [ ] Share access credentials securely

---

## Success Criteria

✅ **Deployment Successful When:**
- Application loads without errors
- Users can sign in/up
- Medical records can be created/viewed
- AI assistant responds to questions
- Mobile access works via QR code
- All API endpoints functional

🎉 **Your MediKey application is now live and fully functional!**

**Deployed URL:** `https://your-project-name.vercel.app`

Remember to:
- Monitor usage and costs
- Keep dependencies updated
- Backup important data
- Monitor performance metrics
