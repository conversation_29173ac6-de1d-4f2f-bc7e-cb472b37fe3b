# Database connection string (PostgreSQL)
# For Neon: ****************************************************************
# For Supabase: ********************************************/postgres
DATABASE_URL=****************************************************************

# Session secret for cookie encryption (generate a random string)
SESSION_SECRET=your-random-session-secret-here-make-it-long-and-secure

# OpenAI API key for AI features
# Get from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-your-openai-api-key-here

# Environment (development/production)
NODE_ENV=development

# Optional: Custom port for local development
PORT=5000
