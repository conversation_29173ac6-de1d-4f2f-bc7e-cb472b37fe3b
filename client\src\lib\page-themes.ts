// Define theme colors for different pages
export type ThemeColor = {
  primary: string;
  primaryDark: string;
  primaryLight: string;
  accent: string;
  background: string;
};

export type PageTheme = {
  [key: string]: ThemeColor;
};

// Define the themes for each page
export const pageThemes: PageTheme = {
  // Blue theme for Dashboard
  dashboard: {
    primary: "hsl(221.2 83.2% 53.3%)",
    primaryDark: "hsl(221.2 83.2% 43.3%)",
    primaryLight: "hsl(221.2 83.2% 93.3%)",
    accent: "hsl(217.2 91.2% 59.8%)",
    background: "hsl(210 40% 98%)",
  },
  // Turquoise theme for Records
  records: {
    primary: "hsl(174 72% 45%)",
    primaryDark: "hsl(174 72% 35%)",
    primaryLight: "hsl(174 72% 95%)",
    accent: "hsl(174 72% 50%)",
    background: "hsl(174 40% 98%)",
  },
  // Green theme for Family
  family: {
    primary: "hsl(142.1 76.2% 36.3%)",
    primaryDark: "hsl(142.1 76.2% 26.3%)",
    primaryLight: "hsl(142.1 76.2% 96.3%)",
    accent: "hsl(142.1 70.6% 45.3%)",
    background: "hsl(140 40% 98%)",
  },
  // Pink theme for Appointments
  appointments: {
    primary: "hsl(330 90% 60%)",
    primaryDark: "hsl(330 90% 50%)",
    primaryLight: "hsl(330 90% 95%)",
    accent: "hsl(330 90% 65%)",
    background: "hsl(330 40% 98%)",
  },
  // Orange theme for Profile
  profile: {
    primary: "hsl(24.6 95% 53.1%)",
    primaryDark: "hsl(24.6 95% 43.1%)",
    primaryLight: "hsl(24.6 95% 93.1%)",
    accent: "hsl(24.6 95% 58.1%)",
    background: "hsl(25 40% 98%)",
  },
  // Red theme for Emergency
  emergency: {
    primary: "hsl(0 72.2% 50.6%)",
    primaryDark: "hsl(0 72.2% 40.6%)",
    primaryLight: "hsl(0 72.2% 95.6%)",
    accent: "hsl(0 72.2% 55.6%)",
    background: "hsl(0 40% 98%)",
  },
  // Yellow theme for Analytics
  analytics: {
    primary: "hsl(45 93% 47%)",
    primaryDark: "hsl(45 93% 37%)",
    primaryLight: "hsl(45 93% 95%)",
    accent: "hsl(45 93% 52%)",
    background: "hsl(45 40% 98%)",
  },
  // Amber theme for AI Assistant
  assistant: {
    primary: "hsl(37.7 92.1% 50.2%)",
    primaryDark: "hsl(37.7 92.1% 40.2%)",
    primaryLight: "hsl(37.7 92.1% 95.2%)",
    accent: "hsl(37.7 92.1% 55.2%)",
    background: "hsl(40 40% 98%)",
  },
  // Cyan theme for SmartWatch
  smartwatch: {
    primary: "hsl(189.7 94.5% 42.7%)",
    primaryDark: "hsl(189.7 94.5% 32.7%)",
    primaryLight: "hsl(189.7 94.5% 92.7%)",
    accent: "hsl(189.7 94.5% 47.7%)",
    background: "hsl(190 40% 98%)",
  },
  // Orange theme for Health
  health: {
    primary: "hsl(24.6 95% 53.1%)",
    primaryDark: "hsl(24.6 95% 43.1%)",
    primaryLight: "hsl(24.6 95% 93.1%)",
    accent: "hsl(24.6 95% 58.1%)",
    background: "hsl(25 40% 98%)",
  },
};

// Get the theme for a specific page
export function getPageTheme(pathname: string): ThemeColor {
  // Extract the page name from the pathname
  const pageName = pathname.split('/')[1] || 'dashboard';

  // Return the theme for the page or the default theme if not found
  return pageThemes[pageName] || pageThemes.dashboard;
}

// Apply the theme to the document
export function applyTheme(theme: ThemeColor): void {
  // Get the current theme mode (light or dark)
  const isDarkMode = document.documentElement.classList.contains('dark-theme');

  // Apply the theme colors
  document.documentElement.style.setProperty('--primary', theme.primary);
  document.documentElement.style.setProperty('--primary-dark', theme.primaryDark);
  document.documentElement.style.setProperty('--primary-light', theme.primaryLight);
  document.documentElement.style.setProperty('--accent', theme.accent);

  // Only set the background color in light mode
  // In dark mode, we use the dark theme background from CSS
  if (!isDarkMode) {
    document.documentElement.style.setProperty('--background', theme.background);
  }

  // Add a custom property for the theme color to use in both modes
  document.documentElement.style.setProperty('--theme-color', theme.primary);
  document.documentElement.style.setProperty('--theme-color-dark', theme.primaryDark);
  document.documentElement.style.setProperty('--theme-color-light', theme.primaryLight);
}
