"use client";

import { useEffect, useState } from "react";
import QRCode from "react-qr-code";
import { motion } from "framer-motion";
import { Smartphone } from "lucide-react";

export function QRCodeAccess() {
  const [url, setUrl] = useState("");
  const [isScanning, setIsScanning] = useState(false);

  useEffect(() => {
    // Using the Vercel deployment URL
    const mobileAccessURL = "https://medikey.vercel.app/";
    setUrl(mobileAccessURL);
  }, []);

  if (!url) return null;

  return (
    <div className="flex flex-col items-center gap-4">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="relative"
        whileHover={{ scale: 1.02 }}
        onHoverStart={() => setIsScanning(true)}
        onHoverEnd={() => setIsScanning(false)}
      >
        {/* Scanning effect overlay */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-b from-transparent via-primary-100/20 to-transparent z-10 pointer-events-none"
          initial={{ opacity: 0, y: -180 }}
          animate={{
            opacity: isScanning ? 1 : 0,
            y: isScanning ? 180 : -180
          }}
          transition={{
            repeat: isScanning ? Infinity : 0,
            duration: 1.5,
            ease: "linear"
          }}
        />

        {/* QR Code */}
        <QRCode value={url} size={180} bgColor="#ffffff" fgColor="#000000" />

        {/* Corner decorations */}
        <motion.div
          className="absolute top-0 left-0 w-5 h-5 border-t-2 border-l-2 border-primary-500"
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 2, repeat: Infinity }}
        />
        <motion.div
          className="absolute top-0 right-0 w-5 h-5 border-t-2 border-r-2 border-primary-500"
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
        />
        <motion.div
          className="absolute bottom-0 left-0 w-5 h-5 border-b-2 border-l-2 border-primary-500"
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 2, repeat: Infinity, delay: 1 }}
        />
        <motion.div
          className="absolute bottom-0 right-0 w-5 h-5 border-b-2 border-r-2 border-primary-500"
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 2, repeat: Infinity, delay: 1.5 }}
        />
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3, duration: 0.5 }}
        className="flex items-center gap-2"
      >
        <motion.div
          animate={{ rotate: [0, 10, 0, -10, 0] }}
          transition={{ duration: 2, repeat: Infinity, repeatType: "loop" }}
        >
          <Smartphone className="h-4 w-4 text-primary-500" />
        </motion.div>
        <p className="text-sm text-gray-600 dark:text-gray-300 text-center max-w-sm">
          Scan this QR code using your mobile phone to instantly access your MediKey dashboard at medikey.vercel.app.
        </p>
      </motion.div>
    </div>
  );
}
