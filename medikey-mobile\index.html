<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>MediVault Mobile</title>

  <!-- PWA Support -->
  <link rel="manifest" href="manifest.json">
  <meta name="theme-color" content="#3b82f6">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="apple-mobile-web-app-title" content="MediVault">

  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <style>
    /* Base Styles and Variables */
    :root {
      /* Light Theme Colors */
      --background: #ffffff;
      --foreground: #1e293b;
      
      --primary: #3b82f6;
      --primary-dark: #2563eb;
      --primary-light: #dbeafe;
      --primary-foreground: #ffffff;
      
      --accent: #60a5fa;
      --accent-foreground: #1e293b;
      
      --muted: #f1f5f9;
      --muted-foreground: #64748b;
      
      --border: #e2e8f0;
      --input: #e2e8f0;
      
      --card: #ffffff;
      --card-foreground: #1e293b;
      
      --destructive: #ef4444;
      --destructive-foreground: #ffffff;
      
      --success: #10b981;
      --success-foreground: #ffffff;
      
      --warning: #f59e0b;
      --warning-foreground: #ffffff;
      
      --radius: 0.5rem;
      
      /* Animation */
      --transition-fast: 0.15s ease;
      --transition-normal: 0.3s ease;
      --transition-slow: 0.5s ease;
      
      /* Shadows */
      --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    /* Dark Theme Colors */
    .dark-theme {
      --background: #0f172a;
      --foreground: #f8fafc;
      
      --primary: #3b82f6;
      --primary-dark: #1d4ed8;
      --primary-light: #1e3a8a;
      --primary-foreground: #ffffff;
      
      --accent: #60a5fa;
      --accent-foreground: #f8fafc;
      
      --muted: #1e293b;
      --muted-foreground: #94a3b8;
      
      --border: #334155;
      --input: #1e293b;
      
      --card: #1e293b;
      --card-foreground: #f8fafc;
    }

    /* Reset and Base Styles */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    html, body {
      height: 100%;
      width: 100%;
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 16px;
      line-height: 1.5;
      color: var(--foreground);
      background-color: var(--background);
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      overflow-x: hidden;
      transition: background-color 0.3s ease, color 0.3s ease;
    }

    /* Layout */
    .app-container {
      display: flex;
      flex-direction: column;
      height: 100vh;
      width: 100%;
      overflow: hidden;
    }

    .header {
      display: flex;
      align-items: center;
      padding: 0.75rem 1rem;
      background-color: var(--primary);
      color: white;
      box-shadow: var(--shadow);
      z-index: 10;
    }

    .header h1 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
    }

    .main-content {
      flex: 1;
      padding: 1rem;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
    }

    /* Card */
    .card {
      background-color: var(--card);
      border-radius: 0.5rem;
      box-shadow: var(--shadow-lg);
      width: 100%;
      max-width: 500px;
      overflow: hidden;
      margin-bottom: 1rem;
    }

    .card-header {
      padding: 1.5rem;
      border-bottom: 1px solid var(--border);
      background-color: var(--primary-light);
    }

    .card-title {
      font-size: 1.25rem;
      color: var(--primary);
      margin: 0;
      display: flex;
      align-items: center;
    }

    .card-title i {
      margin-right: 0.5rem;
    }

    .card-body {
      padding: 1.5rem;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0.75rem 1.5rem;
      background-color: var(--primary);
      color: white;
      border: none;
      border-radius: 0.25rem;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s;
      text-decoration: none;
      margin: 0.5rem;
    }

    .btn:hover {
      background-color: var(--primary-dark);
    }

    .btn i {
      margin-right: 0.5rem;
    }

    /* Animation */
    .bg-animation {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
      overflow: hidden;
      pointer-events: none;
    }

    .molecule {
      position: absolute;
      border-radius: 50%;
      background-color: var(--primary);
      opacity: 0.05;
      animation: float 20s infinite ease-in-out;
    }

    @keyframes float {
      0%, 100% {
        transform: translateY(0) translateX(0);
      }
      25% {
        transform: translateY(-20px) translateX(10px);
      }
      50% {
        transform: translateY(0) translateX(20px);
      }
      75% {
        transform: translateY(20px) translateX(10px);
      }
    }

    /* Loading */
    .loading {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: var(--background);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 100;
      transition: opacity 0.5s, visibility 0.5s;
    }

    .loading.hidden {
      opacity: 0;
      visibility: hidden;
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba(59, 130, 246, 0.3);
      border-radius: 50%;
      border-top-color: var(--primary);
      animation: spin 1s linear infinite;
      margin-bottom: 1rem;
    }

    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }

    /* Dark mode toggle */
    .theme-toggle {
      position: absolute;
      top: 1rem;
      right: 1rem;
      background: none;
      border: none;
      color: var(--foreground);
      font-size: 1.25rem;
      cursor: pointer;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.2s;
    }

    .theme-toggle:hover {
      background-color: var(--muted);
    }

    .logo {
      width: 80px;
      height: 80px;
      margin-bottom: 1rem;
    }

    .app-title {
      font-size: 2rem;
      font-weight: 700;
      margin-bottom: 1rem;
      color: var(--primary);
    }

    .app-description {
      margin-bottom: 2rem;
      max-width: 600px;
    }
  </style>
</head>
<body>
  <!-- Loading Screen -->
  <div class="loading" id="loading">
    <div class="spinner"></div>
    <p>Loading MediVault...</p>
  </div>

  <!-- Background Animation -->
  <div class="bg-animation" id="bg-animation"></div>

  <!-- Theme Toggle -->
  <button class="theme-toggle" id="theme-toggle">
    <i class="fas fa-moon"></i>
  </button>

  <!-- Main App Container -->
  <div class="app-container">
    <!-- Header -->
    <header class="header">
      <h1>MediVault Mobile</h1>
    </header>

    <!-- Main Content -->
    <main class="main-content">
      <div class="logo-container">
        <i class="fas fa-heartbeat logo" style="font-size: 80px; color: var(--primary);"></i>
      </div>
      <h1 class="app-title">MediVault Mobile</h1>
      <p class="app-description">
        Access your health data on the go with MediVault Mobile. Connect to your MediVault account and manage your health information from anywhere.
      </p>
      
      <a href="mobile-connect.html" class="btn">
        <i class="fas fa-qrcode"></i>
        Connect to MediVault
      </a>
      
      <a href="https://medikey.vercel.app/" class="btn">
        <i class="fas fa-external-link-alt"></i>
        Open MediVault Directly
      </a>
    </main>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      console.log('MediVault Mobile Initialized');
      
      // Create background animation
      createBackgroundAnimation();
      
      // Initialize UI components
      initUI();
      
      // Hide loading screen after a short delay
      setTimeout(function() {
        document.getElementById('loading').classList.add('hidden');
      }, 1000);
    });
    
    // Create background animation with molecules
    function createBackgroundAnimation() {
      const bgAnimation = document.getElementById('bg-animation');
      const moleculeCount = 15;
      
      for (let i = 0; i < moleculeCount; i++) {
        const molecule = document.createElement('div');
        molecule.className = 'molecule';
        
        // Random size between 50px and 150px
        const size = Math.random() * 100 + 50;
        molecule.style.width = `${size}px`;
        molecule.style.height = `${size}px`;
        
        // Random position
        molecule.style.top = `${Math.random() * 100}%`;
        molecule.style.left = `${Math.random() * 100}%`;
        
        // Random animation delay
        molecule.style.animationDelay = `${Math.random() * 5}s`;
        
        bgAnimation.appendChild(molecule);
      }
    }
    
    // Initialize UI components
    function initUI() {
      // Theme toggle
      const themeToggle = document.getElementById('theme-toggle');
      const themeIcon = themeToggle.querySelector('i');
      
      themeToggle.addEventListener('click', function() {
        document.body.classList.toggle('dark-theme');
        
        if (document.body.classList.contains('dark-theme')) {
          themeIcon.className = 'fas fa-sun';
        } else {
          themeIcon.className = 'fas fa-moon';
        }
      });
    }
  </script>
</body>
</html>
