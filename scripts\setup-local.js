#!/usr/bin/env node

import fs from 'fs';
import { execSync } from 'child_process';
import crypto from 'crypto';

console.log('🚀 Setting up MediKey for local development...');

try {
  // 1. Check if .env exists
  if (!fs.existsSync('.env')) {
    console.log('📝 Creating .env file from template...');
    
    // Read .env.example
    const envExample = fs.readFileSync('.env.example', 'utf8');
    
    // Generate a random session secret
    const sessionSecret = crypto.randomBytes(32).toString('hex');
    
    // Replace placeholders
    let envContent = envExample
      .replace('your-random-session-secret-here-make-it-long-and-secure', sessionSecret)
      .replace('development', 'development');
    
    // Write .env file
    fs.writeFileSync('.env', envContent);
    console.log('✅ .env file created');
    console.log('⚠️  Please update DATABASE_URL and OPENAI_API_KEY in .env file');
  } else {
    console.log('✅ .env file already exists');
  }

  // 2. Install dependencies
  console.log('📦 Installing dependencies...');
  execSync('npm install', { stdio: 'inherit' });

  // 3. Check if we can connect to database
  console.log('🔍 Checking database connection...');
  try {
    execSync('npm run db:test', { stdio: 'inherit' });
  } catch (error) {
    console.log('⚠️  Database connection failed. Please set up your DATABASE_URL in .env');
  }

  console.log('\n✅ Setup completed!');
  console.log('\n📋 Next steps:');
  console.log('1. Update .env file with your database URL and OpenAI API key');
  console.log('2. Run: npm run db:migrate (to set up database tables)');
  console.log('3. Run: npm run dev (to start development server)');
  console.log('\n🔗 Useful commands:');
  console.log('- npm run dev          # Start development server');
  console.log('- npm run db:test      # Test database connection');
  console.log('- npm run db:migrate   # Run database migrations');
  console.log('- npm run build        # Build for production');

} catch (error) {
  console.error('❌ Setup failed:', error.message);
  process.exit(1);
}
