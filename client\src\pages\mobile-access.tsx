import { QRCodeAccess } from "@/components/ui/qr-code-access";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { Smartphone, QrCode } from "lucide-react";
import { motion } from "framer-motion";
import { useState } from "react";

export default function MobileAccess() {
  const [isHovering, setIsHovering] = useState(false);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="container mx-auto py-6 px-4 sm:px-6 lg:px-8"
    >
      {/* Page Header */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2, duration: 0.5 }}
        className="mb-6"
      >
        <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">Mobile Access</h1>
        <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
          Scan the QR code to access your health data on your mobile device via medikey.vercel.app.
        </p>
      </motion.div>

      {/* QR Code Card */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.3, duration: 0.5 }}
        whileHover={{
          boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
          translateY: -5
        }}
        onHoverStart={() => setIsHovering(true)}
        onHoverEnd={() => setIsHovering(false)}
      >
        <Card className="mb-6 overflow-hidden">
          <CardHeader className="bg-primary-50 dark:bg-primary-900/20">
            <CardTitle className="text-primary-800 dark:text-primary-300 flex items-center">
              <motion.div
                animate={{ rotate: isHovering ? 360 : 0 }}
                transition={{ duration: 1, ease: "easeInOut" }}
              >
                <Smartphone className="h-5 w-5 mr-2 text-primary-600 dark:text-primary-400" />
              </motion.div>
              Try in your device
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6 flex flex-col items-center justify-center">
            <QRCodeAccess />
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}
