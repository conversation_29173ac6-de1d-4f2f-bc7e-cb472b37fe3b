# MediKey Vercel Deployment Guide

This guide will help you deploy your MediKey healthcare application to Vercel with a fully functional PostgreSQL database.

## Prerequisites

- GitHub account
- Vercel account (free)
- Neon account (free PostgreSQL database)

## Step 1: Set Up Free PostgreSQL Database (Neon)

1. **Go to Neon** (https://neon.tech)
2. **Sign up** for a free account
3. **Create a new project**:
   - Project name: `medikey-db`
   - Database name: `medikey`
   - Region: Choose closest to your users
4. **Get your connection string**:
   - Go to your project dashboard
   - Click on "Connection Details"
   - Copy the connection string (it looks like: `****************************************************************`)

## Step 2: Prepare Your Repository

1. **Push your code to GitHub**:
   ```bash
   git add .
   git commit -m "Prepare for Vercel deployment"
   git push origin main
   ```

## Step 3: Deploy to Vercel

1. **Go to Vercel** (https://vercel.com)
2. **Sign up/Login** with your GitHub account
3. **Import your repository**:
   - Click "New Project"
   - Select your MediKey repository
   - Click "Import"

4. **Configure build settings**:
   - Framework Preset: Other
   - Build Command: `npm run vercel-build`
   - Output Directory: `dist`
   - Install Command: `npm install`

## Step 4: Configure Environment Variables

In your Vercel project dashboard, go to Settings > Environment Variables and add:

```
DATABASE_URL=your_neon_connection_string_here
SESSION_SECRET=your_random_session_secret_here
OPENAI_API_KEY=your_openai_api_key_here
NODE_ENV=production
```

### Getting OpenAI API Key (Free Tier)

1. Go to https://platform.openai.com/
2. Sign up for an account
3. Go to API Keys section
4. Create a new API key
5. Copy the key (starts with `sk-`)

**Note**: OpenAI provides $5 free credits for new accounts.

## Step 5: Deploy

1. **Click "Deploy"** in Vercel
2. **Wait for deployment** to complete
3. **Check the deployment logs** for any errors

## Step 6: Initialize Database

After successful deployment:

1. **Visit your deployed app** (Vercel will provide the URL)
2. **The database will auto-initialize** on first visit
3. **Default login credentials**:
   - Username: `manasvi`
   - Password: `password123`

## Step 7: Test Functionality

Test these features:

1. **Authentication**: Sign in/out
2. **Medical Records**: Upload and view records
3. **AI Assistant**: Ask health questions
4. **Mobile Access**: Scan QR code
5. **Dashboard**: View health metrics

## Troubleshooting

### Common Issues

1. **Database Connection Error**:
   - Verify DATABASE_URL is correct
   - Check Neon database is active
   - Ensure SSL is enabled

2. **Build Failures**:
   - Check build logs in Vercel
   - Verify all dependencies are in package.json
   - Ensure TypeScript compiles locally

3. **API Routes Not Working**:
   - Check vercel.json configuration
   - Verify API routes start with `/api/`

4. **OpenAI API Errors**:
   - Verify API key is correct
   - Check you have credits remaining
   - Ensure key has proper permissions

### Environment Variables

Make sure these are set in Vercel:

- `DATABASE_URL`: Your Neon PostgreSQL connection string
- `SESSION_SECRET`: Random string for session encryption
- `OPENAI_API_KEY`: Your OpenAI API key
- `NODE_ENV`: Set to `production`

## Free Tier Limitations

- **Neon**: 3GB storage, 1 database
- **Vercel**: 100GB bandwidth, 6000 build minutes
- **OpenAI**: $5 free credits (usually lasts months for testing)

## Custom Domain (Optional)

1. Go to your Vercel project settings
2. Click "Domains"
3. Add your custom domain
4. Follow DNS configuration instructions

## Monitoring

- **Vercel Analytics**: Built-in performance monitoring
- **Database Monitoring**: Available in Neon dashboard
- **Error Tracking**: Check Vercel function logs

## Security Notes

- All connections use SSL/TLS
- Session data is encrypted
- API keys are stored securely in Vercel
- Database credentials are not exposed to client

## Support

If you encounter issues:

1. Check Vercel deployment logs
2. Verify environment variables
3. Test database connection
4. Check OpenAI API status

Your MediKey application should now be fully deployed and functional!
