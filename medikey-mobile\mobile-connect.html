<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>MediVault Mobile - Connect</title>

  <!-- PWA Support -->
  <link rel="manifest" href="manifest.json">
  <meta name="theme-color" content="#3b82f6">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="apple-mobile-web-app-title" content="MediVault">

  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="./images/favicon.svg">
  <link rel="apple-touch-icon" href="./images/icon-192x192.png">

  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- QR Code Library -->
  <script src="https://cdn.jsdelivr.net/npm/qrcode-generator@1.4.4/qrcode.min.js"></script>
  
  <style>
    /* Base Styles and Variables */
    :root {
      /* Light Theme Colors */
      --background: #ffffff;
      --foreground: #1e293b;
      
      --primary: #3b82f6;
      --primary-dark: #2563eb;
      --primary-light: #dbeafe;
      --primary-foreground: #ffffff;
      
      --accent: #60a5fa;
      --accent-foreground: #1e293b;
      
      --muted: #f1f5f9;
      --muted-foreground: #64748b;
      
      --border: #e2e8f0;
      --input: #e2e8f0;
      
      --card: #ffffff;
      --card-foreground: #1e293b;
      
      --destructive: #ef4444;
      --destructive-foreground: #ffffff;
      
      --success: #10b981;
      --success-foreground: #ffffff;
      
      --warning: #f59e0b;
      --warning-foreground: #ffffff;
      
      --radius: 0.5rem;
      
      /* Animation */
      --transition-fast: 0.15s ease;
      --transition-normal: 0.3s ease;
      --transition-slow: 0.5s ease;
      
      /* Shadows */
      --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    /* Dark Theme Colors */
    .dark-theme {
      --background: #0f172a;
      --foreground: #f8fafc;
      
      --primary: #3b82f6;
      --primary-dark: #1d4ed8;
      --primary-light: #1e3a8a;
      --primary-foreground: #ffffff;
      
      --accent: #60a5fa;
      --accent-foreground: #f8fafc;
      
      --muted: #1e293b;
      --muted-foreground: #94a3b8;
      
      --border: #334155;
      --input: #1e293b;
      
      --card: #1e293b;
      --card-foreground: #f8fafc;
    }

    /* Reset and Base Styles */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    html, body {
      height: 100%;
      width: 100%;
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 16px;
      line-height: 1.5;
      color: var(--foreground);
      background-color: var(--background);
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      overflow-x: hidden;
      transition: background-color 0.3s ease, color 0.3s ease;
    }

    /* Typography */
    h1, h2, h3, h4, h5, h6 {
      font-weight: 600;
      line-height: 1.2;
      margin-bottom: 0.5rem;
    }

    h1 {
      font-size: 1.5rem;
    }

    h2 {
      font-size: 1.25rem;
    }

    h3 {
      font-size: 1.125rem;
    }

    p {
      margin-bottom: 1rem;
    }

    /* Layout */
    .app-container {
      display: flex;
      flex-direction: column;
      height: 100vh;
      width: 100%;
      overflow: hidden;
    }

    .header {
      display: flex;
      align-items: center;
      padding: 0.75rem 1rem;
      background-color: var(--primary);
      color: white;
      box-shadow: var(--shadow);
      z-index: 10;
    }

    .header h1 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
    }

    .menu-button {
      background: none;
      border: none;
      color: white;
      font-size: 1.5rem;
      margin-right: 1rem;
      cursor: pointer;
    }

    .header-icons {
      margin-left: auto;
      display: flex;
      gap: 1rem;
    }

    .header-icon {
      background: none;
      border: none;
      color: white;
      font-size: 1.25rem;
      cursor: pointer;
    }

    .main-content {
      flex: 1;
      padding: 1rem;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    /* Card */
    .card {
      background-color: white;
      border-radius: 0.5rem;
      box-shadow: var(--shadow-lg);
      width: 100%;
      max-width: 500px;
      overflow: hidden;
    }

    .card-header {
      padding: 1.5rem;
      border-bottom: 1px solid var(--border);
      background-color: var(--primary-light);
    }

    .card-title {
      font-size: 1.25rem;
      color: var(--primary);
      margin: 0;
      display: flex;
      align-items: center;
    }

    .card-title i {
      margin-right: 0.5rem;
    }

    .card-body {
      padding: 1.5rem;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .qr-container {
      background-color: white;
      padding: 1rem;
      border-radius: 0.5rem;
      margin-bottom: 1.5rem;
      box-shadow: var(--shadow);
      position: relative;
      overflow: hidden;
    }

    .qr-scan-line {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background-color: var(--primary);
      box-shadow: 0 0 8px 2px var(--primary);
      animation: scan 2s linear infinite;
      opacity: 0.7;
    }

    @keyframes scan {
      0% {
        top: 0;
      }
      100% {
        top: 100%;
      }
    }

    .instructions {
      text-align: center;
      margin-bottom: 1.5rem;
    }

    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0.5rem 1rem;
      background-color: var(--primary);
      color: white;
      border: none;
      border-radius: 0.25rem;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s;
      text-decoration: none;
    }

    .btn:hover {
      background-color: var(--primary-dark);
    }

    .btn i {
      margin-right: 0.5rem;
    }

    /* Animation */
    .bg-animation {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
      overflow: hidden;
      pointer-events: none;
    }

    .molecule {
      position: absolute;
      border-radius: 50%;
      background-color: var(--primary);
      opacity: 0.05;
      animation: float 20s infinite ease-in-out;
    }

    @keyframes float {
      0%, 100% {
        transform: translateY(0) translateX(0);
      }
      25% {
        transform: translateY(-20px) translateX(10px);
      }
      50% {
        transform: translateY(0) translateX(20px);
      }
      75% {
        transform: translateY(20px) translateX(10px);
      }
    }

    /* Loading */
    .loading {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: var(--background);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 100;
      transition: opacity 0.5s, visibility 0.5s;
    }

    .loading.hidden {
      opacity: 0;
      visibility: hidden;
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba(59, 130, 246, 0.3);
      border-radius: 50%;
      border-top-color: var(--primary);
      animation: spin 1s linear infinite;
      margin-bottom: 1rem;
    }

    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }

    /* Dark mode toggle */
    .theme-toggle {
      position: absolute;
      top: 1rem;
      right: 1rem;
      background: none;
      border: none;
      color: var(--foreground);
      font-size: 1.25rem;
      cursor: pointer;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.2s;
    }

    .theme-toggle:hover {
      background-color: var(--muted);
    }

    /* Back button */
    .back-button {
      position: absolute;
      top: 1rem;
      left: 1rem;
      background: none;
      border: none;
      color: var(--foreground);
      font-size: 1.25rem;
      cursor: pointer;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.2s;
    }

    .back-button:hover {
      background-color: var(--muted);
    }
  </style>
</head>
<body>
  <!-- Loading Screen -->
  <div class="loading" id="loading">
    <div class="spinner"></div>
    <p>Loading MediVault...</p>
  </div>

  <!-- Background Animation -->
  <div class="bg-animation" id="bg-animation"></div>

  <!-- Theme Toggle -->
  <button class="theme-toggle" id="theme-toggle">
    <i class="fas fa-moon"></i>
  </button>

  <!-- Back Button -->
  <button class="back-button" id="back-button">
    <i class="fas fa-arrow-left"></i>
  </button>

  <!-- Main App Container -->
  <div class="app-container">
    <!-- Header -->
    <header class="header">
      <h1>Mobile Connect</h1>
    </header>

    <!-- Main Content -->
    <main class="main-content">
      <div class="card">
        <div class="card-header">
          <h2 class="card-title">
            <i class="fas fa-qrcode"></i>
            Connect to MediVault
          </h2>
        </div>
        <div class="card-body">
          <div class="qr-container">
            <div class="qr-scan-line"></div>
            <div id="qrcode"></div>
          </div>
          <div class="instructions">
            <p>Scan this QR code with your mobile device to connect to MediVault.</p>
            <p>This will take you to the online version of MediVault at <strong>medikey.vercel.app</strong>.</p>
          </div>
          <a href="https://medikey.vercel.app/" class="btn" target="_blank">
            <i class="fas fa-external-link-alt"></i>
            Open MediVault
          </a>
        </div>
      </div>
    </main>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      console.log('MediVault Mobile Connect Initialized');
      
      // Create background animation
      createBackgroundAnimation();
      
      // Generate QR code
      generateQRCode();
      
      // Initialize UI components
      initUI();
      
      // Hide loading screen after a short delay
      setTimeout(function() {
        document.getElementById('loading').classList.add('hidden');
      }, 1000);
    });
    
    // Create background animation with molecules
    function createBackgroundAnimation() {
      const bgAnimation = document.getElementById('bg-animation');
      const moleculeCount = 15;
      
      for (let i = 0; i < moleculeCount; i++) {
        const molecule = document.createElement('div');
        molecule.className = 'molecule';
        
        // Random size between 50px and 150px
        const size = Math.random() * 100 + 50;
        molecule.style.width = `${size}px`;
        molecule.style.height = `${size}px`;
        
        // Random position
        molecule.style.top = `${Math.random() * 100}%`;
        molecule.style.left = `${Math.random() * 100}%`;
        
        // Random animation delay
        molecule.style.animationDelay = `${Math.random() * 5}s`;
        
        bgAnimation.appendChild(molecule);
      }
    }
    
    // Generate QR code
    function generateQRCode() {
      const qrContainer = document.getElementById('qrcode');
      const url = "https://medikey.vercel.app/";
      
      // Create QR code
      const qr = qrcode(0, 'M');
      qr.addData(url);
      qr.make();
      
      // Render QR code
      qrContainer.innerHTML = qr.createImgTag(5, 10);
      
      // Style the QR code
      const qrImg = qrContainer.querySelector('img');
      qrImg.style.display = 'block';
      qrImg.style.margin = '0 auto';
    }
    
    // Initialize UI components
    function initUI() {
      // Theme toggle
      const themeToggle = document.getElementById('theme-toggle');
      const themeIcon = themeToggle.querySelector('i');
      
      themeToggle.addEventListener('click', function() {
        document.body.classList.toggle('dark-theme');
        
        if (document.body.classList.contains('dark-theme')) {
          themeIcon.className = 'fas fa-sun';
        } else {
          themeIcon.className = 'fas fa-moon';
        }
      });
      
      // Back button
      const backButton = document.getElementById('back-button');
      
      backButton.addEventListener('click', function() {
        window.location.href = 'index.html';
      });
    }
  </script>
</body>
</html>
