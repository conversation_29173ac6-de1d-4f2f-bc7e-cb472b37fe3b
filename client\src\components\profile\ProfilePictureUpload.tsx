import { useState, useRef } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { Camera, Loader2, Upload, X } from "lucide-react";
import { motion } from "framer-motion";
import { apiRequest } from "@/lib/queryClient";

interface ProfilePictureUploadProps {
  username: string;
  avatarUrl?: string;
}

export default function ProfilePictureUpload({ username, avatarUrl }: ProfilePictureUploadProps) {
  const [file, setFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const uploadAvatarMutation = useMutation({
    mutationFn: async (formData: FormData) => {
      // In a real implementation, this would be a proper file upload endpoint
      // For now, we'll simulate it by updating the user profile with a URL
      const mockAvatarUrl = URL.createObjectURL(file!);
      
      // Update the user profile with the avatar URL
      return apiRequest("PATCH", "/api/users/profile", {
        avatarUrl: mockAvatarUrl
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/users/profile"] });
      toast({
        title: "Profile picture updated",
        description: "Your profile picture has been updated successfully.",
      });
      
      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    },
    onError: (error) => {
      toast({
        title: "Update failed",
        description: `Failed to update profile picture: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      setFile(selectedFile);
      
      // Create a preview URL
      const objectUrl = URL.createObjectURL(selectedFile);
      setPreviewUrl(objectUrl);
    }
  };

  const handleUpload = () => {
    if (!file) return;
    
    const formData = new FormData();
    formData.append("avatar", file);
    uploadAvatarMutation.mutate(formData);
  };

  const handleRemove = () => {
    setFile(null);
    setPreviewUrl(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="flex flex-col items-center space-y-4">
      <motion.div
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        className="relative"
      >
        <Avatar className="h-24 w-24 cursor-pointer" onClick={handleBrowseClick}>
          <AvatarImage 
            src={previewUrl || avatarUrl || ""} 
            alt={username} 
            className="object-cover"
          />
          <AvatarFallback className="bg-primary-100 text-primary-800 text-xl">
            {username.charAt(0).toUpperCase()}
          </AvatarFallback>
        </Avatar>
        <motion.div 
          className="absolute bottom-0 right-0 bg-primary-500 rounded-full p-1 cursor-pointer"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={handleBrowseClick}
        >
          <Camera className="h-4 w-4 text-white" />
        </motion.div>
      </motion.div>
      
      <input
        ref={fileInputRef}
        type="file"
        onChange={handleFileChange}
        className="hidden"
        accept="image/*"
      />
      
      {previewUrl && (
        <motion.div 
          className="flex space-x-2"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Button
            type="button"
            size="sm"
            variant="outline"
            onClick={handleRemove}
          >
            <X className="h-4 w-4 mr-1" />
            Cancel
          </Button>
          <Button
            type="button"
            size="sm"
            onClick={handleUpload}
            disabled={uploadAvatarMutation.isPending}
          >
            {uploadAvatarMutation.isPending ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-1" />
                Saving...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-1" />
                Save
              </>
            )}
          </Button>
        </motion.div>
      )}
      
      <p className="text-xs text-gray-500 text-center">
        Click on the avatar to upload a profile picture
      </p>
    </div>
  );
}
