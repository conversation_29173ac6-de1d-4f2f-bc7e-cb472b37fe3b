import { useTheme } from "@/hooks/useTheme";
import { motion } from "framer-motion";
import { Moon, Sun } from "lucide-react";
import { useLocation } from "wouter";
import { getPageTheme } from "@/lib/page-themes";

export function ThemeToggle() {
  const { theme, toggleTheme } = useTheme();
  const [location] = useLocation();
  const pageTheme = getPageTheme(location);

  return (
    <motion.button
      onClick={toggleTheme}
      className="fixed bottom-4 right-4 z-50 p-3 rounded-full shadow-lg"
      style={{
        backgroundColor: theme === 'dark'
          ? 'hsl(217, 33%, 17%)'
          : 'white',
        boxShadow: theme === 'dark'
          ? `0 4px 12px ${pageTheme.primary}40`
          : `0 4px 12px ${pageTheme.primary}40`,
        border: `2px solid ${pageTheme.primary}`
      }}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {theme === 'dark' ? (
        <motion.div
          initial={{ rotate: -30, opacity: 0 }}
          animate={{ rotate: 0, opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <Sun className="h-6 w-6 text-yellow-300" />
        </motion.div>
      ) : (
        <motion.div
          initial={{ rotate: 30, opacity: 0 }}
          animate={{ rotate: 0, opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <Moon className="h-6 w-6" style={{ color: pageTheme.primary }} />
        </motion.div>
      )}
    </motion.button>
  );
}
