import { motion } from "framer-motion";
import { ReactNode } from "react";

// Animation variants for different elements
export const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  }
};

export const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 }
  }
};

export const iconVariants = {
  hidden: { scale: 0.8, opacity: 0 },
  visible: {
    scale: 1,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 15
    }
  },
  hover: {
    scale: 1.1,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 10
    }
  }
};

export const cardHoverVariants = {
  hover: {
    y: -5,
    boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
    transition: { duration: 0.2 }
  }
};

interface AnimatedContainerProps {
  children: ReactNode;
  className?: string;
  delay?: number;
  as?: "div" | "section" | "main";
}

export function AnimatedContainer({ 
  children, 
  className = "", 
  delay = 0,
  as = "div"
}: AnimatedContainerProps) {
  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      transition={{ delay }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

interface AnimatedItemProps {
  children: ReactNode;
  className?: string;
  delay?: number;
  whileHover?: any;
  whileTap?: any;
  viewport?: any;
}

export function AnimatedItem({ 
  children, 
  className = "", 
  delay = 0,
  whileHover,
  whileTap,
  viewport
}: AnimatedItemProps) {
  return (
    <motion.div
      variants={itemVariants}
      transition={{ delay }}
      className={className}
      whileHover={whileHover}
      whileTap={whileTap}
      viewport={viewport}
    >
      {children}
    </motion.div>
  );
}

interface AnimatedCardProps {
  children: ReactNode;
  className?: string;
  delay?: number;
}

export function AnimatedCard({ 
  children, 
  className = "", 
  delay = 0 
}: AnimatedCardProps) {
  return (
    <motion.div
      variants={itemVariants}
      transition={{ delay }}
      whileHover={cardHoverVariants.hover}
      className={className}
    >
      {children}
    </motion.div>
  );
}

interface AnimatedIconProps {
  children: ReactNode;
  className?: string;
  animate?: any;
}

export function AnimatedIcon({ 
  children, 
  className = "",
  animate
}: AnimatedIconProps) {
  return (
    <motion.div
      variants={iconVariants}
      whileHover="hover"
      animate={animate}
      className={className}
    >
      {children}
    </motion.div>
  );
}
