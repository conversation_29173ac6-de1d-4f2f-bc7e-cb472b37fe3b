#!/usr/bin/env node

import fs from 'fs';
import { execSync } from 'child_process';

console.log('🔍 MediKey Vercel Deployment Troubleshooter');
console.log('==========================================\n');

// Check 1: Environment file
console.log('1. 📋 Checking environment configuration...');
if (fs.existsSync('.env')) {
  const envContent = fs.readFileSync('.env', 'utf8');
  const hasDatabase = envContent.includes('DATABASE_URL=');
  const hasOpenAI = envContent.includes('OPENAI_API_KEY=');
  const hasSession = envContent.includes('SESSION_SECRET=');
  
  console.log(`   ✅ .env file exists`);
  console.log(`   ${hasDatabase ? '✅' : '❌'} DATABASE_URL configured`);
  console.log(`   ${hasOpenAI ? '✅' : '❌'} OPENAI_API_KEY configured`);
  console.log(`   ${hasSession ? '✅' : '❌'} SESSION_SECRET configured`);
} else {
  console.log('   ❌ .env file missing');
}

// Check 2: Package.json scripts
console.log('\n2. 📦 Checking package.json scripts...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const hasVercelBuild = packageJson.scripts && packageJson.scripts['vercel-build'];
const hasBuild = packageJson.scripts && packageJson.scripts['build'];

console.log(`   ${hasVercelBuild ? '✅' : '❌'} vercel-build script exists`);
console.log(`   ${hasBuild ? '✅' : '❌'} build script exists`);

// Check 3: Vercel configuration
console.log('\n3. ⚙️  Checking Vercel configuration...');
if (fs.existsSync('vercel.json')) {
  console.log('   ✅ vercel.json exists');
  const vercelConfig = JSON.parse(fs.readFileSync('vercel.json', 'utf8'));
  console.log('   📄 Vercel config:', JSON.stringify(vercelConfig, null, 2));
} else {
  console.log('   ❌ vercel.json missing');
}

// Check 4: Dependencies
console.log('\n4. 📚 Checking critical dependencies...');
const deps = packageJson.dependencies || {};
const requiredDeps = ['postgres', 'express', 'drizzle-orm', 'openai'];

requiredDeps.forEach(dep => {
  console.log(`   ${deps[dep] ? '✅' : '❌'} ${dep}: ${deps[dep] || 'missing'}`);
});

// Check 5: Build test
console.log('\n5. 🏗️  Testing build process...');
try {
  console.log('   🔄 Running build test...');
  execSync('npm run vercel-build', { stdio: 'pipe' });
  console.log('   ✅ Build successful');
} catch (error) {
  console.log('   ❌ Build failed:', error.message);
}

// Check 6: Database connection
console.log('\n6. 🗄️  Testing database connection...');
try {
  execSync('npm run db:test', { stdio: 'pipe' });
  console.log('   ✅ Database connection successful');
} catch (error) {
  console.log('   ❌ Database connection failed');
}

// Check 7: File structure
console.log('\n7. 📁 Checking file structure...');
const requiredFiles = [
  'server/index.ts',
  'migrations/0001_initial.sql',
  'dist/index.js',
  'dist/public/index.html'
];

requiredFiles.forEach(file => {
  console.log(`   ${fs.existsSync(file) ? '✅' : '❌'} ${file}`);
});

console.log('\n🎯 Troubleshooting Summary:');
console.log('==========================');
console.log('If you see ❌ above, those are issues to fix.');
console.log('\nCommon Vercel deployment issues:');
console.log('1. Missing environment variables in Vercel dashboard');
console.log('2. Build command not set correctly');
console.log('3. Database URL not accessible from Vercel');
console.log('4. OpenAI API key invalid or missing');
console.log('\nNext steps:');
console.log('1. Fix any ❌ issues above');
console.log('2. Check Vercel deployment logs');
console.log('3. Verify environment variables in Vercel dashboard');
console.log('4. Test database connection from Vercel environment');
