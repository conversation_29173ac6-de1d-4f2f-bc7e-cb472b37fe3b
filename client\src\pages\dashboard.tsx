import EmergencyInfoCard from "@/components/dashboard/EmergencyInfoCard";
import MedicalSummaryCard from "@/components/dashboard/MedicalSummaryCard";
import AppointmentsCard from "@/components/dashboard/AppointmentsCard";
import HealthAnalyticsSection from "@/components/dashboard/HealthAnalyticsSection";
import RecentMedicalRecordsSection from "@/components/dashboard/RecentMedicalRecordsSection";
import FamilyVaultSection from "@/components/dashboard/FamilyVaultSection";
import AIAssistantCard from "@/components/dashboard/AIAssistantCard";
import { LiveHealthMetrics, SmartWatchIntegration } from "@/components/smartwatch";
import { useQuery } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";
import { useAuth } from "@/hooks/useAuth";
import { Shield, Calendar, Heart, FileText, Users, Bot, Watch } from "lucide-react";
import { Link } from "wouter";
import { motion } from "framer-motion";

export default function Dashboard() {
  const { user } = useAuth();

  const { data: userProfile, isLoading: userLoading } = useQuery({
    queryKey: ["/api/users/profile"],
  });

  if (userLoading) {
    return <DashboardSkeleton />;
  }

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  const iconVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 15
      }
    },
    hover: {
      scale: 1.1,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 10
      }
    }
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="py-6 px-4 sm:px-6 lg:px-8"
    >
      {/* Page Header */}
      <motion.div
        variants={itemVariants}
        className="mb-6"
      >
        <h1 className="text-2xl font-semibold text-gray-900">Health Dashboard</h1>
        <p className="mt-1 text-sm text-gray-600">
          Welcome back, {userProfile?.fullName || user?.username}. Here's your health overview.
        </p>
      </motion.div>

      {/* Quick Access Feature Navigation */}
      <motion.div
        variants={containerVariants}
        className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6"
      >
        <Link href="/emergency">
          <motion.a
            variants={itemVariants}
            whileHover={{
              y: -5,
              boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
              backgroundColor: "var(--primary-50)"
            }}
            className="bg-white rounded-lg shadow-sm p-4 flex flex-col items-center text-center"
          >
            <motion.div
              variants={iconVariants}
              whileHover="hover"
              className="h-12 w-12 rounded-full bg-primary-100 flex items-center justify-center mb-3"
            >
              <Shield className="h-6 w-6 text-primary-600" />
            </motion.div>
            <h3 className="text-sm font-medium text-gray-900">Emergency Access</h3>
            <p className="text-xs text-gray-500 mt-1">Critical health info</p>
          </motion.a>
        </Link>

        <Link href="/records">
          <motion.a
            variants={itemVariants}
            whileHover={{
              y: -5,
              boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
              backgroundColor: "var(--primary-50)"
            }}
            className="bg-white rounded-lg shadow-sm p-4 flex flex-col items-center text-center"
          >
            <motion.div
              variants={iconVariants}
              whileHover="hover"
              className="h-12 w-12 rounded-full bg-primary-100 flex items-center justify-center mb-3"
            >
              <FileText className="h-6 w-6 text-primary-600" />
            </motion.div>
            <h3 className="text-sm font-medium text-gray-900">Medical Records</h3>
            <p className="text-xs text-gray-500 mt-1">View & manage records</p>
          </motion.a>
        </Link>

        <Link href="/appointments">
          <motion.a
            variants={itemVariants}
            whileHover={{
              y: -5,
              boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
              backgroundColor: "var(--primary-50)"
            }}
            className="bg-white rounded-lg shadow-sm p-4 flex flex-col items-center text-center"
          >
            <motion.div
              variants={iconVariants}
              whileHover="hover"
              className="h-12 w-12 rounded-full bg-primary-100 flex items-center justify-center mb-3"
            >
              <Calendar className="h-6 w-6 text-primary-600" />
            </motion.div>
            <h3 className="text-sm font-medium text-gray-900">Appointments</h3>
            <p className="text-xs text-gray-500 mt-1">Schedule & track visits</p>
          </motion.a>
        </Link>

        <Link href="/analytics">
          <motion.a
            variants={itemVariants}
            whileHover={{
              y: -5,
              boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
              backgroundColor: "var(--primary-50)"
            }}
            className="bg-white rounded-lg shadow-sm p-4 flex flex-col items-center text-center"
          >
            <motion.div
              variants={iconVariants}
              whileHover="hover"
              className="h-12 w-12 rounded-full bg-primary-100 flex items-center justify-center mb-3"
            >
              <Heart className="h-6 w-6 text-primary-600" />
            </motion.div>
            <h3 className="text-sm font-medium text-gray-900">Health Analytics</h3>
            <p className="text-xs text-gray-500 mt-1">Track your metrics</p>
          </motion.a>
        </Link>

        <Link href="/family">
          <motion.a
            variants={itemVariants}
            whileHover={{
              y: -5,
              boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
              backgroundColor: "var(--primary-50)"
            }}
            className="bg-white rounded-lg shadow-sm p-4 flex flex-col items-center text-center"
          >
            <motion.div
              variants={iconVariants}
              whileHover="hover"
              className="h-12 w-12 rounded-full bg-primary-100 flex items-center justify-center mb-3"
            >
              <Users className="h-6 w-6 text-primary-600" />
            </motion.div>
            <h3 className="text-sm font-medium text-gray-900">Family Vault</h3>
            <p className="text-xs text-gray-500 mt-1">Manage dependents</p>
          </motion.a>
        </Link>

        <Link href="/assistant">
          <motion.a
            variants={itemVariants}
            whileHover={{
              y: -5,
              boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
              backgroundColor: "var(--primary-50)"
            }}
            className="bg-white rounded-lg shadow-sm p-4 flex flex-col items-center text-center"
          >
            <motion.div
              variants={iconVariants}
              whileHover="hover"
              className="h-12 w-12 rounded-full bg-primary-100 flex items-center justify-center mb-3"
            >
              <Bot className="h-6 w-6 text-primary-600" />
            </motion.div>
            <h3 className="text-sm font-medium text-gray-900">AI Assistant</h3>
            <p className="text-xs text-gray-500 mt-1">Get health answers</p>
          </motion.a>
        </Link>
      </motion.div>

      {/* Emergency Information Card */}
      <motion.div
        variants={itemVariants}
      >
        <EmergencyInfoCard userProfile={userProfile} />
      </motion.div>

      {/* Dashboard Content */}
      <motion.div
        variants={containerVariants}
        className="grid grid-cols-1 gap-6 lg:grid-cols-3"
      >
        {/* Medical Summary Card */}
        <motion.div
          variants={itemVariants}
          className="lg:col-span-2"
          whileHover={{
            y: -3,
            transition: { duration: 0.2 }
          }}
        >
          <MedicalSummaryCard className="h-full" />
        </motion.div>

        {/* Upcoming Appointments Card */}
        <motion.div
          variants={itemVariants}
          whileHover={{
            y: -3,
            transition: { duration: 0.2 }
          }}
        >
          <AppointmentsCard />
        </motion.div>
      </motion.div>

      {/* Health Analytics Section */}
      <motion.div
        variants={itemVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.2 }}
      >
        <HealthAnalyticsSection />
      </motion.div>

      {/* Recent Medical Records Section */}
      <motion.div
        variants={itemVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.2 }}
      >
        <RecentMedicalRecordsSection />
      </motion.div>

      {/* Family Vault Section */}
      <motion.div
        variants={itemVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.2 }}
      >
        <FamilyVaultSection />
      </motion.div>

      {/* Smartwatch Integration */}
      <motion.div
        variants={itemVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.2 }}
        className="mb-6"
      >
        <motion.h2
          className="text-xl font-medium mb-4 flex items-center gap-2"
          whileHover={{ x: 5 }}
        >
          <motion.div
            animate={{
              rotate: [0, 10, 0, -10, 0],
            }}
            transition={{
              duration: 5,
              repeat: Infinity,
              repeatType: "loop"
            }}
          >
            <Watch className="h-5 w-5" />
          </motion.div>
          Smartwatch Integration
        </motion.h2>
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <motion.div whileHover={{ y: -3, transition: { duration: 0.2 } }}>
            <LiveHealthMetrics />
          </motion.div>
          <motion.div whileHover={{ y: -3, transition: { duration: 0.2 } }}>
            <SmartWatchIntegration userId={user?.id || 0} />
          </motion.div>
        </div>
      </motion.div>

      {/* AI Assistant Card */}
      <motion.div
        variants={itemVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.2 }}
        whileHover={{
          y: -3,
          transition: { duration: 0.2 }
        }}
      >
        <AIAssistantCard />
      </motion.div>
    </motion.div>
  );
}

function DashboardSkeleton() {
  return (
    <div className="py-6 px-4 sm:px-6 lg:px-8">
      <div className="mb-6">
        <Skeleton className="h-8 w-64 mb-2" />
        <Skeleton className="h-4 w-48" />
      </div>

      {/* Quick Access Feature Navigation Skeleton */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
        {[...Array(6)].map((_, index) => (
          <div key={index} className="bg-white rounded-lg shadow-sm p-4">
            <div className="flex flex-col items-center">
              <Skeleton className="h-12 w-12 rounded-full mb-3" />
              <Skeleton className="h-4 w-24 mb-2" />
              <Skeleton className="h-3 w-16" />
            </div>
          </div>
        ))}
      </div>

      {/* Emergency Info Card Skeleton */}
      <Skeleton className="h-48 w-full mb-6" />

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3 mb-6">
        <Skeleton className="h-80 lg:col-span-2" />
        <Skeleton className="h-80" />
      </div>

      <Skeleton className="h-64 w-full mb-6" />
      <Skeleton className="h-80 w-full mb-6" />
      <Skeleton className="h-64 w-full mb-6" />
      <Skeleton className="h-40 w-full" />
    </div>
  );
}
