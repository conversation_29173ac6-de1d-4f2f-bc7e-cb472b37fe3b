import { useLocation } from "wouter";
import { motion } from "framer-motion";
import { getPageTheme } from "@/lib/page-themes";
import { useTheme } from "@/hooks/useTheme";

export function ThemeIndicator() {
  const [location] = useLocation();
  const pageName = location.split('/')[1] || 'dashboard';
  const pageTheme = getPageTheme(location);
  const { theme } = useTheme();

  // Capitalize the first letter of the page name
  const displayName = pageName.charAt(0).toUpperCase() + pageName.slice(1);

  // Determine text color based on theme mode
  const textColor = theme === 'dark' ? 'white' : 'white';

  // Determine background color based on theme mode
  const backgroundColor = pageTheme.primary;

  // Determine glow color based on theme mode
  const glowColor = theme === 'dark'
    ? `${pageTheme.primary}90`
    : `${pageTheme.primary}80`;

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="fixed top-4 right-4 z-50 px-4 py-2 rounded-full text-xs font-bold shadow-lg flex items-center"
      style={{
        backgroundColor: backgroundColor,
        color: textColor,
        boxShadow: `0 4px 14px 0 ${glowColor}`,
        border: theme === 'dark' ? `1px solid ${pageTheme.primaryLight}` : 'none'
      }}
      whileHover={{
        scale: 1.05,
        boxShadow: `0 6px 20px 0 ${glowColor}`
      }}
    >
      <span
        className="inline-block w-2 h-2 rounded-full mr-1.5"
        style={{
          backgroundColor: textColor,
          boxShadow: `0 0 5px ${textColor}80`
        }}
      />
      {displayName} {theme === 'dark' ? 'Dark' : 'Light'}
    </motion.div>
  );
}
