import { useLocation } from 'wouter';
import { getPageTheme } from '@/lib/page-themes';
import { MoleculeAnimation } from './molecule-animation';
import { useTheme } from '@/hooks/useTheme';

export function AnimatedBackground() {
  const [location] = useLocation();
  const pageTheme = getPageTheme(location);
  const { theme } = useTheme();

  // Parse the theme color to get RGB values for the animation
  const primaryColor = pageTheme.primary;
  const rgbMatch = primaryColor.match(/hsl\((\d+\.?\d*)\s+(\d+\.?\d*)%\s+(\d+\.?\d*)%\)/);

  let h = 221.2; // Default hue (blue)
  let s = 83.2;  // Default saturation
  let l = 53.3;  // Default lightness

  if (rgbMatch && rgbMatch.length >= 4) {
    h = parseFloat(rgbMatch[1]);
    s = parseFloat(rgbMatch[2]);
    l = parseFloat(rgbMatch[3]);
  }

  // Adjust lightness for dark mode to make it more visible
  if (theme === 'dark') {
    // Increase lightness in dark mode for better visibility
    l = Math.min(l + 20, 90);
    s = Math.min(s + 10, 100);
  }

  // Convert HSL to hex for the molecule animation
  const hslToHex = (h: number, s: number, l: number): string => {
    l /= 100;
    const a = s * Math.min(l, 1 - l) / 100;
    const f = (n: number) => {
      const k = (n + h / 30) % 12;
      const color = l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);
      return Math.round(255 * color).toString(16).padStart(2, '0');
    };
    return `#${f(0)}${f(8)}${f(4)}`;
  };

  const hexColor = hslToHex(h, s, l);

  // Determine the number of nodes based on the page
  const getNodeCount = (): number => {
    const pageName = location.split('/')[1] || 'dashboard';

    // Different pages can have different node counts for variety
    switch(pageName) {
      case 'dashboard': return 20;
      case 'records': return 15;
      case 'family': return 25;
      case 'appointments': return 18;
      case 'profile': return 12;
      case 'emergency': return 30;
      case 'analytics': return 22;
      default: return 15;
    }
  };

  // Set opacity based on theme
  const opacity = theme === 'dark' ? 0.25 : 0.15;

  return (
    <MoleculeAnimation
      color={hexColor}
      nodeCount={getNodeCount()}
      opacity={opacity}
    />
  );
}
