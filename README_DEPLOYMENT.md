# MediKey Healthcare Application - Deployment Guide

A comprehensive healthcare management application with AI-powered features, deployed on Vercel with PostgreSQL database.

## 🚀 Quick Start

### Option 1: Automated Setup
```bash
npm run setup
```

### Option 2: Manual Setup
1. Clone the repository
2. Copy `.env.example` to `.env`
3. Update environment variables
4. Run `npm install`
5. Run `npm run db:migrate`
6. Run `npm run dev`

## 🏗️ Architecture

- **Frontend**: React + TypeScript + Tailwind CSS
- **Backend**: Node.js + Express
- **Database**: PostgreSQL (Neon/Supabase)
- **AI**: OpenAI GPT integration
- **Deployment**: Vercel
- **Authentication**: Session-based with bcrypt

## 📋 Features

### Core Features
- ✅ User Authentication (Sign-up/Sign-in)
- ✅ Medical Records Management
- ✅ Health Metrics Tracking
- ✅ Appointment Scheduling
- ✅ Family Health Vault
- ✅ AI Health Assistant
- ✅ Mobile Access with QR Code
- ✅ Emergency Information Access

### AI Features
- 🤖 Health question answering
- 📄 Medical document analysis
- 📊 Health summary generation
- 💡 Personalized health insights

## 🌐 Deployment Options

### 1. Vercel (Recommended)
```bash
# Deploy to Vercel
npm run deploy:vercel
```

### 2. Manual Deployment
1. Set up PostgreSQL database (Neon/Supabase)
2. Configure environment variables
3. Build the application
4. Deploy to Vercel

## 🗄️ Database Setup

### Free PostgreSQL Options

#### Option 1: Neon (Recommended)
1. Go to [neon.tech](https://neon.tech)
2. Create free account
3. Create new project
4. Copy connection string
5. Add to `.env` as `DATABASE_URL`

#### Option 2: Supabase
1. Go to [supabase.com](https://supabase.com)
2. Create free account
3. Create new project
4. Go to Settings > Database
5. Copy connection string
6. Add to `.env` as `DATABASE_URL`

### Database Migration
```bash
npm run db:migrate
```

## 🔑 Environment Variables

Required variables for deployment:

```env
DATABASE_URL=**********************************************
SESSION_SECRET=your-random-secret-key
OPENAI_API_KEY=sk-your-openai-key
NODE_ENV=production
```

### Getting OpenAI API Key
1. Go to [platform.openai.com](https://platform.openai.com)
2. Create account (free $5 credits)
3. Go to API Keys
4. Create new key
5. Copy and add to environment variables

## 🛠️ Development

### Local Development
```bash
npm run dev
```

### Testing Database
```bash
npm run db:test
```

### Building for Production
```bash
npm run build
```

## 📱 Mobile Access

The application includes a mobile-friendly interface accessible via QR code:
- Scan QR code from Mobile Access page
- Direct link to deployed application
- Responsive design for mobile devices

## 🔒 Security Features

- Password hashing with bcrypt
- Session-based authentication
- SSL/TLS encryption
- Environment variable protection
- SQL injection prevention
- XSS protection

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Error**
   ```bash
   npm run db:test
   ```
   - Check DATABASE_URL format
   - Verify database is accessible
   - Ensure SSL is enabled for production

2. **Build Failures**
   - Check TypeScript compilation: `npm run check`
   - Verify all dependencies: `npm install`
   - Check build logs in Vercel

3. **API Errors**
   - Verify OpenAI API key
   - Check API rate limits
   - Ensure proper environment variables

### Debug Commands
```bash
npm run db:test      # Test database connection
npm run check        # TypeScript check
npm run build        # Test build process
```

## 📊 Monitoring

- **Vercel Analytics**: Built-in performance monitoring
- **Database Monitoring**: Available in Neon/Supabase dashboard
- **Error Tracking**: Check Vercel function logs

## 💰 Cost Breakdown (Free Tier)

- **Vercel**: Free (100GB bandwidth, 6000 build minutes)
- **Neon**: Free (3GB storage, 1 database)
- **OpenAI**: $5 free credits (lasts months for testing)
- **Total**: $0 to start, minimal costs for production

## 🔄 Updates and Maintenance

### Updating the Application
1. Push changes to GitHub
2. Vercel auto-deploys from main branch
3. Database migrations run automatically

### Backup Strategy
- Neon: Automatic daily backups
- Supabase: Point-in-time recovery
- Code: Version controlled in GitHub

## 📞 Support

For deployment issues:
1. Check deployment logs in Vercel
2. Verify environment variables
3. Test database connection
4. Review error messages

## 🎯 Production Checklist

- [ ] Database set up and accessible
- [ ] Environment variables configured
- [ ] OpenAI API key added
- [ ] Domain configured (optional)
- [ ] SSL certificate active
- [ ] Database migrations run
- [ ] Application tested end-to-end

Your MediKey application is now ready for production deployment! 🎉
